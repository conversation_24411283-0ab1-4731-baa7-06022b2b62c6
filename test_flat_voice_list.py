#!/usr/bin/env python3
"""
Simple test to verify the flat voice list format
"""
import requests
import json

def test_flat_voice_list():
    """Test that the voice list returns a flat array instead of grouped by language"""
    url = "http://127.0.0.1:9011/voice_list"
    
    print("Testing Flat Voice List Format")
    print("=" * 40)
    
    # Test with OpenAI TTS (should return simple list)
    print("\n1. Testing OpenAI TTS (tts_type=8):")
    try:
        response = requests.post(url, json={"tts_type": 8})
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                voices = data["data"]["voices"]
                print(f"✓ Response format: {type(voices).__name__}")
                print(f"✓ Voices: {voices}")
                
                # Verify it's a list, not a dict
                if isinstance(voices, list):
                    print("✓ Correct format: flat list")
                else:
                    print("✗ Wrong format: expected list, got dict")
            else:
                print(f"✗ API Error: {data.get('msg')}")
        else:
            print(f"✗ HTTP Error: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("✗ Connection Error: API server not running")
        return
    except Exception as e:
        print(f"✗ Error: {str(e)}")
    
    # Test with language filter
    print("\n2. Testing with language filter (Edge-TTS, zh):")
    try:
        response = requests.post(url, json={"tts_type": 0, "language": "zh"})
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                voices = data["data"]["voices"]
                print(f"✓ Response format: {type(voices).__name__}")
                print(f"✓ Voice count: {len(voices)}")
                
                # Show sample voices
                if voices:
                    sample = voices[:3]
                    print(f"✓ Sample voices: {sample}")
                    
                    # Check if they contain Chinese indicators
                    chinese_voices = [v for v in voices if 'zh-' in v.lower() or 'chinese' in v.lower() or '中文' in v]
                    print(f"✓ Chinese voices found: {len(chinese_voices)}")
                
                # Verify it's a list
                if isinstance(voices, list):
                    print("✓ Correct format: flat list")
                else:
                    print("✗ Wrong format: expected list")
            else:
                print(f"✗ API Error: {data.get('msg')}")
        else:
            print(f"✗ HTTP Error: {response.status_code}")
    except Exception as e:
        print(f"✗ Error: {str(e)}")
    
    # Test multiple services to verify consistency
    test_services = [
        {"tts_type": 2, "name": "ChatTTS"},
        {"tts_type": 10, "name": "Google TTS"},
        {"tts_type": 16, "name": "Gemini TTS"}
    ]
    
    print("\n3. Testing multiple services for format consistency:")
    for service in test_services:
        try:
            response = requests.post(url, json={"tts_type": service["tts_type"]})
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    voices = data["data"]["voices"]
                    format_type = type(voices).__name__
                    voice_count = len(voices) if isinstance(voices, list) else "N/A"
                    print(f"  {service['name']}: {format_type}, {voice_count} voices")
                else:
                    print(f"  {service['name']}: API Error - {data.get('msg')}")
            else:
                print(f"  {service['name']}: HTTP Error {response.status_code}")
        except Exception as e:
            print(f"  {service['name']}: Error - {str(e)}")
    
    print("\n" + "=" * 40)
    print("Test completed!")

if __name__ == "__main__":
    test_flat_voice_list()
