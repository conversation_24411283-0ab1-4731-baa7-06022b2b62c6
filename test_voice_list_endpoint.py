#!/usr/bin/env python3
"""
Test script for the new /voice_list endpoint
"""
import requests
import json

def test_voice_list_endpoint():
    """Test the voice list endpoint with different TTS services"""
    base_url = "http://127.0.0.1:9011"
    endpoint = f"{base_url}/voice_list"
    
    # Test cases for different TTS services
    test_cases = [
        {"tts_type": 0, "description": "Edge-TTS"},
        {"tts_type": 1, "description": "CosyVoice"},
        {"tts_type": 2, "description": "ChatTTS"},
        {"tts_type": 3, "description": "302.AI"},
        {"tts_type": 5, "description": "Azure-TTS"},
        {"tts_type": 8, "description": "OpenAI TTS"},
        {"tts_type": 9, "description": "Elevenlabs.io"},
        {"tts_type": 10, "description": "Google TTS"},
    ]
    
    print("Testing /voice_list endpoint...")
    print("=" * 50)
    
    for test_case in test_cases:
        print(f"\nTesting {test_case['description']} (tts_type: {test_case['tts_type']})")
        print("-" * 40)
        
        try:
            # Test without language filter
            response = requests.post(endpoint, json={
                "tts_type": test_case["tts_type"]
            })
            
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Status: {response.status_code}")
                print(f"✓ Response code: {data.get('code')}")
                print(f"✓ Service name: {data.get('data', {}).get('service_name')}")
                print(f"✓ Service ID: {data.get('data', {}).get('service_id')}")
                
                voices = data.get('data', {}).get('voices', {})
                print(f"✓ Available languages: {list(voices.keys())}")
                
                # Show sample voices for each language
                for lang, voice_list in voices.items():
                    sample_count = min(3, len(voice_list))
                    sample_voices = voice_list[:sample_count]
                    print(f"  - {lang}: {sample_voices}{'...' if len(voice_list) > 3 else ''} ({len(voice_list)} total)")
                    
            else:
                print(f"✗ HTTP Error: {response.status_code}")
                print(f"✗ Response: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("✗ Connection Error: API server is not running")
            print("  Please start the API server first by running: python api.py")
            break
        except Exception as e:
            print(f"✗ Error: {str(e)}")
    
    # Test with language filter
    print(f"\n\nTesting with language filter (zh)...")
    print("-" * 40)
    try:
        response = requests.post(endpoint, json={
            "tts_type": 0,  # Edge-TTS
            "language": "zh"
        })
        
        if response.status_code == 200:
            data = response.json()
            voices = data.get('data', {}).get('voices', {})
            print(f"✓ Filtered languages: {list(voices.keys())}")
        else:
            print(f"✗ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"✗ Error: {str(e)}")
    
    # Test error cases
    print(f"\n\nTesting error cases...")
    print("-" * 40)
    
    error_test_cases = [
        {"data": {}, "description": "Missing tts_type"},
        {"data": {"tts_type": "invalid"}, "description": "Invalid tts_type (string)"},
        {"data": {"tts_type": -1}, "description": "Invalid tts_type (negative)"},
        {"data": {"tts_type": 999}, "description": "Invalid tts_type (too large)"},
    ]
    
    for error_case in error_test_cases:
        try:
            response = requests.post(endpoint, json=error_case["data"])
            data = response.json()
            print(f"✓ {error_case['description']}: code={data.get('code')}, msg='{data.get('msg')}'")
        except Exception as e:
            print(f"✗ {error_case['description']}: {str(e)}")

if __name__ == "__main__":
    test_voice_list_endpoint()
